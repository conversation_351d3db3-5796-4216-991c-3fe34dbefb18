// 分区管理

import request from '/@/utils/request';
//仓库下拉
export const getWarehouse = () => {
	return request({
		url: '/admin/warehouse/selectWarehouseList',
		method: 'get',
	});
};
//区域下拉
export const getArea = (id: string) => {
	return request({
		url: '/admin/warehouseZone/selectWarehouseZoneList/' + id,
		method: 'get',
	});
};
//物资名称 联想
export const getMaterialName = (params?: Object) => {
	return request({
		url: '/admin/materialCatalog/getMaterialName',
		method: 'get',
		params,
	});
};
export const msPageList = (obj?: Object) => {
	return request({
		url: '/admin/materialOnStatistic/getMaterialOnStatisticPage',
		method: 'post',
		data: obj,
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});
};

export const moPageList = (obj?: Object) => {
	return request({
		url: '/admin/materialOutStatistic/getMaterialOnStatisticPage',
		method: 'post',
		data: obj,
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});
};

export const sPageList = (obj?: Object) => {
	return request({
		url: '/admin/inboundStatistics/getInboundStatisticsPage',
		method: 'post',
		data: obj,
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});
};

export const oPageList = (obj?: Object) => {
	return request({
		url: '/admin/outboundStatistics/getOutboundStatisticsPage',
		method: 'post',
		data: obj,
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});
};

export const allocationPageList = (obj?: Object) => {
	return request({
		url: '/admin/allocationStatistics/getAllocationStatisticsPage',
		method: 'post',
		data: obj,
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});
};

import request from '/@/utils/request';
//分页查询
export const pageList = (params?: Object) => {
	return request({
		url: '/admin/materialScenePlan/getMaterialScenePlanPage',
		method: 'get',
		params,
	});
};
//新增
export const addObj = (obj: Object) => {
	return request({
		url: '/admin/materialScenePlan/addMaterialScenePlan',
		method: 'post',
		data: obj,
	});
};
//修改
export const putObj = (obj: Object) => {
	return request({
		url: '/admin/materialScenePlan/updateMaterialScenePlan',
		method: 'post',
		data: obj,
	});
};
//删除
export function delObj(id?: string) {
	return request({
		url: '/admin/materialScenePlan/deleteMaterialScenePlanById/' + id,
		method: 'get',
	});
}
//写标签
export const writeAboutCousins = (id: String) => {
	return request({
		url: '/admin/materialScenePlan/getMaterialScenePlanById/' + id,
		method: 'get',
	});
};
//获取工作台 信息
export const acquisitionDevice = (obj: Object) => {
	return request({
		url: '/admin/materialScenePlan/updateMaterialScenePlanStatus',
		method: 'post',
		data: obj,
	});
};

<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="mb8 w-full">
					<div class="float-left">
						<el-form ref="queryRef" :inline="true" :model="state.queryForm" @keyup.enter="getDataList">
							<el-form-item label="仓库" prop="logType">
								<el-select placeholder="请选择仓库" clearable v-model="state.queryForm.logType">
									<el-option :key="item.value" :label="item.label" :value="item.value" v-for="item in warnType" />
								</el-select>
							</el-form-item>
							<el-form-item label="物资名称" prop="phone">
								<el-input v-model="state.queryForm.phone" placeholder="请输入物资名称" clearable style="max-width: 180px" />
							</el-form-item>
							<el-form-item label="物资条码" prop="createTime">
								<div class="flex">
									<el-input v-model="state.queryForm.phone" placeholder="例：20250215000100012F000100" clearable class="mr-1 w-[240px]" />
									<span>—</span>
									<el-input v-model="state.queryForm.phone" placeholder="20250215000100012F000120" clearable class="ml-1 w-[240px]" />
								</div>
							</el-form-item>
							<el-form-item>
								<el-button icon="Search" type="primary" @click="getDataList">{{ $t('common.queryBtn') }}</el-button>
							</el-form-item>
						</el-form>
					</div>
					<div class="float-right">
						<el-button @click="printLabelClick" class="ml10" formDialogRef icon="printer" type="primary"> 打印标签 </el-button>
					</div>
				</div>
			</el-row>

			<el-table
				:data="state.dataList"
				@selection-change="handleSelectionChange"
				@sort-change="sortChangeHandle"
				class="w-full"
				row-key="id"
				v-loading="state.loading"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column align="center" type="selection" width="40" />
				
				<el-table-column label="序号" type="index" width="60" />
				<el-table-column label="物资编码" prop="name" show-overflow-tooltip />
				<el-table-column label="物资标识" prop="name" show-overflow-tooltip />
				<el-table-column label="物资名称" prop="name" show-overflow-tooltip width="570" />
				<el-table-column label="条码" prop="materialBar" show-overflow-tooltip />
				<el-table-column :label="$t('common.action')" width="150">
					<template #default="scope">
						<el-button icon="edit-pen" @click="handleWrtieRFID(scope.row.materialBar)" text type="primary">写标签 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination @current-change="currentChangeHandle" @size-change="sizeChangeHandle" v-bind="state.pagination" />
		</div>

		<!-- 编辑、新增  -->
	</div>
</template>

<script lang="ts" name="systemSysI18n" setup>
import { BasicTableProps, useTable } from '/@/hooks/table';

import { delObj, fetchList, refreshCache } from '/@/api/admin/i18n';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
import axios from 'axios';
import { getConfigurationObj } from '/@/api/storage/rfidSettings';
// @ts-ignore

import { getLodop } from '../../../../../public/plugin/Lodop/LodopFuncs' //导入模块

// 引入组件
const { t } = useI18n();
// 定义查询字典

// 搜索变量
const queryRef = ref();
const showSearch = ref(true);
// 多选变量
const selectObjs = ref([]) as any;
const multiple = ref(true);

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		name: '',
		zhCn: '',
		en: '',
	},
	pageList: fetchList,
	descs: ['create_time'],
});

//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, sortChangeHandle, downBlobFile, tableStyle } = useTable(state);

// 多选事件
const handleSelectionChange = (objs: { id: string }[]) => {
	selectObjs.value = objs.map(({ id }) => id);
	multiple.value = !objs.length;
};

// 删除操作
const handleDelete = async (ids: string[]) => {
	try {
		await useMessageBox().confirm(t('common.delConfirmText'));
	} catch {
		return;
	}

	try {
		await delObj(ids);
		getDataList();
		useMessage().success(t('common.delSuccessText'));
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};

//获取详情
let Ip = ref('');
let Port = ref('');
let sign = ref('');
const getConfiguration = () => {
	getConfigurationObj().then((res: any) => {
		Ip.value = res.data.ip;
		Port.value = res.data.port;
		sign.value = res.data.materialLabel;
	});
};
// 写标签
const handleWrtieRFID = async (codeNum: any) => {
	const RFIDurl = 'http://' + Ip.value + ':' + Port.value;
	let formData = { cmd: '10007', data: { writeCount: 1, oldepc: '', newepc: codeNum } };
	axios.post(RFIDurl, formData).then((res: any) => {
		let status = res.data.data.status;
		if (status == 0) {
			useMessage().success(t('标签写入成功'));
		} else if (status == -1) {
			useMessage().success(t('正在读卡中'));
		} else if (status == -2) {
			useMessage().success(t('参数错误'));
		} else if (status == -3) {
			useMessage().success(t('epc长度错误'));
		} else if (status == 250) {
			useMessage().success(t('有电子标签，但通讯不畅'));
		} else if (status == 251) {
			useMessage().success(t('无电子标签'));
		} else if (status == 253) {
			useMessage().success(t('命令长度错误'));
		} else {
			useMessage().success(t(`错误编码：${status}`));
		}
	});
};
//打印物资标签
const printLabelClick = async () => {
	try {
		if (!selectObjs.value.length) {
			// 没有选择对象时，确认是否打印所有标签
			await useMessageBox().confirm('是否打印当前页全部标签?');
			state.dataList.forEach((item: any) => {
				printingOperation(item);
			});
		} else {
			selectObjs.value.forEach((item: any) => {
				printingOperation(item);
			});
		}
	} catch (error) {
		console.error('打印操作失败:', error); // 捕获错误并打印更详细的信息
	}
};
//打印货位标签
const printingOperation = (obj: any) => {
	const userAgent = navigator.userAgent.toLowerCase();
	let LODOP = getLodop(); //调用 getLodop获取LODOP对象
	LODOP.PRINT_INITA(0, 0, '100mm', '15mm', '打印二维码');

	if (userAgent.indexOf('win') !== -1) {
		LODOP.SET_PRINT_PAGESIZE(1, '100mm', '15mm', ''); //设定纸张大小
		LODOP.SET_LICENSES('', 'AF8A5800B823915C50BCF67B925E8EA7', '', '');
	}
	if (userAgent.indexOf('linux') !== -1) {
		LODOP.SET_LICENSES('', 'E9663FC92B893B0D4F484B', '', '');
	}

	LODOP.ADD_PRINT_TEXT('7mm', '3mm', '65mm', '7mm', obj.locationCode);
	LODOP.SET_PRINT_STYLEA(0, 'FontSize', 11);
	LODOP.SET_PRINT_STYLEA(0, 'TextNeatRow', true);
	if (sign.value == '1') {
		LODOP.ADD_PRINT_TEXT('11mm', '3mm', '70mm', '8mm', obj.materialCode);
	} else if (sign.value == '2') {
		LODOP.ADD_PRINT_TEXT('11mm', '3mm', '70mm', '8mm', obj.materialIdentify);
	}
	LODOP.SET_PRINT_STYLEA(0, 'FontSize', 11);
	LODOP.ADD_PRINT_BARCODE('3mm', '70mm', '14mm', '14mm', 'QRCode', obj.locationBar);
	LODOP.SET_PRINT_STYLEA(0, 'QRCodeErrorLevel', 'L');
	LODOP.PRINT();
};
onMounted(() => {
	getConfiguration();
});
</script>

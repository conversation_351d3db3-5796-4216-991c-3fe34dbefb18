<template>
	<el-drawer v-model="visible" size="30%" :with-header="false">
		<div class="w-full">
			<el-row>
				<div class="ml-7">
					<span class="mr-3"> 物资名称： </span>
					<span> {{ tableData.materialName }} </span>
				</div>
			</el-row>
			<el-row class="mb-2 mt-2">
				<div>
					<span class="ml-7 mr-3"> 物资编码： </span>
					<span> {{ tableData.materialCode }} </span>
				</div>
			</el-row>
			<el-row>
				<div class="w-full flex justify-between items-center">
					<div class="ml-7">
						<span class="mr-3"> 物资标识： </span>
						<span> {{ tableData.materialIdentify }} </span>
					</div>
					<div>
						<el-button type="primary" @click="printLabelClick">打印标签</el-button>
					</div>
				</div>
			</el-row>
			<el-row class="mb8"> </el-row>
			<el-table :data="tableData.materialList" border :cell-style="tableStyle.cellStyle" :header-cell-style="tableStyle.headerCellStyle"
			max-height="85vh"
			>
				<el-table-column label="序号" type="index" width="60" fixed="left" />
				<el-table-column label="物资条码" prop="materialBar" show-overflow-tooltip width="270"></el-table-column>
				<el-table-column label="状态" show-overflow-tooltip>
					<template #default="scope">
						<span v-if="scope.row.onShelfStatus == '0'">待上架</span>
						<span v-if="scope.row.onShelfStatus == '1'">在库</span>
						<span v-if="scope.row.onShelfStatus == '2'">下架</span>
					</template>
				</el-table-column>
				<el-table-column label="操作" fixed="right">
					<template #default="scope">
						<el-button  text type="primary" @click="handleWrtieRFID(scope.row.materialBar)"> 写标签 </el-button>

					</template>
				</el-table-column>
			</el-table>
		</div>
	</el-drawer>
</template>

<script setup lang="ts" name="log-detail">
import { getMxObj } from '/@/api/storage/warehouseManagement/purchase';
import { useI18n } from 'vue-i18n';
import { useMessage } from '/@/hooks/message';
import { BasicTableProps, useTable } from '/@/hooks/table';
import axios from 'axios';
// @ts-ignore
import { getLodop } from '../../../../../public/plugin/Lodop/LodopFuncs' //导入模块
import {getConfigurationObj} from '/@/api/storage/rfidSettings'
const state: BasicTableProps = reactive<BasicTableProps>({});
const { tableStyle } = useTable(state);

const { t } = useI18n();

// 定义刷新表格emit
const emit = defineEmits(['refresh']);

const visible = ref(false);

// 打开弹窗
const openDialog = async (id: any) => {
	visible.value = true;
	// 加载使用的数据
	getPostData(id);
};
const tableData = ref<any>([]);
const getPostData = async (id: any) => {
	const { data } = await getMxObj(id);
	tableData.value = data;
};
// 暴露变量
defineExpose({
	openDialog,
});



//获取详情
let Ip = ref('')
let Port = ref('')
let sign = ref('')

const getConfiguration = () => {
	getConfigurationObj().then((res: any) => {
		Ip.value = res.data.ip;
		Port.value = res.data.port;
		sign.value = res.data.materialLabel;

	});
};
// 写标签
const handleWrtieRFID = async (codeNum: any) => {

const RFIDurl = 'http://' + Ip.value + ':' + Port.value;
let formData = { cmd: '10007', data: { writeCount: 1, oldepc: '', newepc: codeNum } };
axios.post(RFIDurl, formData).then((res: any) => {
	let status = res.data.data.status;
	if (status == 0) {
		useMessage().success('标签写入成功');
	} else if (status == -1) {
		useMessage().success('正在读卡中');
	} else if (status == -2) {
		useMessage().success('参数错误');
	} else if (status == -3) {
		useMessage().success('epc长度错误');
	} else if (status == 250) {
		useMessage().success('有电子标签，但通讯不畅');
	} else if (status == 251) {
		useMessage().success('无电子标签');
	} else if (status == 253) {
		useMessage().success('命令长度错误');
	} else {
		useMessage().success(`错误编码：${status}`);
	}
});
};







const printLabelClick = async () => {

	tableData.value.materialList.forEach((item: any) => {
		printingOperation(item);
	})

}
//打印货位标签
const printingOperation = (obj: any) => {
	const userAgent = navigator.userAgent.toLowerCase();
	let LODOP = getLodop(); //调用 getLodop获取LODOP对象
	LODOP.PRINT_INITA(0, 0, '100mm', '15mm', '打印二维码');

	if (userAgent.indexOf('win') !== -1) {
		LODOP.SET_PRINT_PAGESIZE(1, '100mm', '15mm', ''); //设定纸张大小
		LODOP.SET_LICENSES('', 'AF8A5800B823915C50BCF67B925E8EA7', '', '');
	}
	if (userAgent.indexOf('linux') !== -1) {
		LODOP.SET_LICENSES('', 'E9663FC92B893B0D4F484B', '', '');
	}

	LODOP.ADD_PRINT_TEXT('7mm', '3mm', '65mm', '7mm', obj.locationCode);
	LODOP.SET_PRINT_STYLEA(0, 'FontSize', 11);
	LODOP.SET_PRINT_STYLEA(0, 'TextNeatRow', true);
	if (sign.value == '1') {
		LODOP.ADD_PRINT_TEXT('11mm', '3mm', '70mm', '8mm', obj.materialCode);
	} else if (sign.value == '2') {
		LODOP.ADD_PRINT_TEXT('11mm', '3mm', '70mm', '8mm', obj.materialIdentify);
	}
	LODOP.SET_PRINT_STYLEA(0, 'FontSize', 11);
	LODOP.ADD_PRINT_BARCODE('3mm', '70mm', '14mm', '14mm', 'QRCode', obj.locationBar);
	LODOP.SET_PRINT_STYLEA(0, 'QRCodeErrorLevel', 'L');
	LODOP.PRINT();
};

onMounted(() => {
	getConfiguration()
});
</script>

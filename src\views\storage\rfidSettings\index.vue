<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view pt-50 pl-20 box-border">
			<el-form ref="dataFormRef" :model="form" :rules="dataRules" formDialogRef label-width="100px" :inline="true">
				<div>
					<el-row class="text-14 font-bold">打印设备配置</el-row>
					<el-form-item :label="$t('print.printDevice')" prop="printingEquipment">
						<el-select v-model="form.printingEquipment" clearable class="w-60" placeholder="请选择打印设备">
							<el-option v-for="item in printing_equipment" :key="item.value" :label="item.label" :value="item.value" />
						</el-select>
					</el-form-item>

					<el-form-item label="厂商" prop="printerManufacturers" v-if="form.printingEquipment === '1'">
						<el-select v-model="form.printerManufacturers" clearable class="w-60" placeholder="请选择打印机厂商">
							<el-option v-for="item in printer_manufacturers" :key="item.value" :label="item.label" :value="item.value" />
						</el-select>
					</el-form-item>
					<el-form-item label="分辨率" prop="printResolution" v-if="form.printingEquipment === '1'">
						<el-select v-model="form.printResolution" clearable class="w-60" placeholder="请选择打印机分辨率">
							<el-option v-for="item in print_resolution" :key="item.value" :label="item.label" :value="item.value" />
						</el-select>
					</el-form-item>
					<el-form-item label="打印机" prop="templateId" v-if="form.printingEquipment === '1'">
						<el-select v-model="form.rfidFour" clearable class="w-60">
							<el-option v-for="item in printer_list" :key="item.id" :label="item.deviceName" :value="item.id" />
						</el-select>
					</el-form-item>
				</div>

				<div>
					<el-row class="text-14 font-bold">标签内容配置</el-row>
					<el-row>
						<el-form-item label="货位标签:"> 货位编码 <span class="ml5 mr5">+</span>二维码 </el-form-item>
					</el-row>
					<el-row>
						<el-form-item label="物资标签:">
							<el-select v-model="form.materialLabel" clearable class="w-60">
								<el-option
									v-for="item in [
										{ label: '物资编码', value: '1' },
										{ label: '物资标识', value: '2' },
									]"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
							<span class="ml10">+二维码</span>
						</el-form-item>
					</el-row>
				</div>
				<div>
					<el-row class="text-14 font-bold">RFID工作台配置</el-row>

					<el-form-item label="RFID工作台" prop="templateId">
						<div class="flex">
							<el-select v-model="form.rfidTwo" clearable class="w-60">
								<el-option v-for="item in workstation_list" :key="item.value" :label="item.deviceName" :value="item.id" />
							</el-select>
							<el-input v-model="form.power" placeholder="请设置工作台功率0-30 " clearable class="!max-w-[180px] ml20 mr20" />
						</div>
					</el-form-item>
					<div>
						<el-button @click="onSubmits" type="primary" :disabled="loading" class="ml-[200px] mt-[40px]">{{
							$t('common.confirmButtonText')
						}}</el-button>
					</div>
				</div>
			</el-form>
		</div>
	</div>
</template>

<script setup lang="ts" name="systemSysMessage">
import { useMessage } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
import { useDict } from '/@/hooks/dict';
import { getConfigurationObj, putObj, getWorkstation, getPrinter } from '/@/api/storage/rfidSettings';
//引入字典
const { printer_manufacturers, print_resolution, printing_equipment } = useDict('printer_manufacturers', 'print_resolution', 'printing_equipment');
// 引入组件
const { t } = useI18n();

// 定义变量内容
const dataFormRef = ref();
const loading = ref(false);

// 提交表单数据
const form = reactive({
	printResolution: '',
	printerManufacturers: '',
	printingEquipment: '',
	rfidFour: '',
	materialLabel: '',
	rfidTwo: '',
	power: '',
});

// 定义校验规则
const dataRules = ref({
	printingEquipment: [{ required: true, message: '打印设备不能为空', trigger: 'blur' }],
	printResolution: [{ required: true, message: '打印机分辨率不能为空', trigger: 'blur' }],
	printerManufacturers: [{ required: true, message: '打印机厂商不能为空', trigger: 'blur' }],
});
//获取详情
const getFormDetails = () => {
	getConfigurationObj().then((res: any) => {
		//将res.data中的数值类型转为字符串

		if (res.data) {
			Object.assign(form, res.data);
		} else {
			form.printingEquipment = '3';
			form.materialLabel = '1';
			form.rfidTwo=workstation_list.value[0].id;
		}
	});
};

// 提交
const onSubmits = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {});
	if (!valid) return false;
	try {
		loading.value = true;
		const res = await putObj(form);
		useMessage().success('设置成功');
	} catch (err: any) {
		useMessage().error(err.msg);
	} finally {
		loading.value = false;
	}
};
//获取工作站  下拉
let workstation_list = ref<any>([]);
const workstation = async () => {
	getWorkstation().then((res: any) => {
		workstation_list.value = res.data;
	});
};
//获取打印机下拉
let printer_list = ref<any>([]);
const printer = async () => {
	getPrinter().then((res: any) => {
		printer_list.value = res.data;
	});
};
//生命周期
onMounted(async () => {
	await workstation();
	await printer();
	getFormDetails();
});
</script>

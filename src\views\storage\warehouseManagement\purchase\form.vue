<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div  class="w-full flex justify-between items-center">
					<div class="flex items-center cursor-pointer" @click="returnClick">
						<img src="/@/assets/back.png" class="w-5 h-5 mr-1" />
						<div class="text-14" style="color: #2e5cf6">返回</div>
					</div>
					<div>
						<el-button @click="exportPdf">导出</el-button>
					</div>
				</div>
			</el-row>
			<el-row class="mt20">
				<el-col :span="24">
					<Descriptions title="入库单" :column="5" :label-style="{ fontSize: '14px' }">
						<template #extra> </template>
						<DescriptionsItem label="入库单编号">{{detailsData?.billCode}}</DescriptionsItem>
						<DescriptionsItem label="入库仓库">{{detailsData?.warehouseName}}</DescriptionsItem>
						<DescriptionsItem label="入库单状态">{{detailsData?.billStatusName}}</DescriptionsItem>
						<DescriptionsItem label="创建人员">{{ detailsData?.createUser }}</DescriptionsItem>
						<DescriptionsItem label="创建时间">{{ detailsData?.createTime }}</DescriptionsItem>

						<DescriptionsItem label="入库部门">{{detailsData?.entryDept}}</DescriptionsItem>
						<DescriptionsItem label="入库人员">{{detailsData?.entryUser}}</DescriptionsItem>
						<DescriptionsItem label="入库时间">{{detailsData?.entryTime}}</DescriptionsItem>
						<DescriptionsItem label="上架人员">{{detailsData?.putShelfUser}}</DescriptionsItem>
						<DescriptionsItem label="上架时间">{{detailsData?.putShelfTime}}</DescriptionsItem>
					</Descriptions>
				</el-col>
			</el-row>

			<el-row>
				<div class="mb-2 w-full" ></div>
			</el-row>
			<el-table
				v-loading="state.loading"
				:data="detailsData?.billDetailList"
				row-key="userId"
				max-height="calc(100vh - 400px)"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column label="物资编码" prop="materialCode" fixed="left" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资标识" prop="materialIdentify" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资名称" prop="materialName" show-overflow-tooltip width="600"></el-table-column>
				<el-table-column label="数量" prop="num" show-overflow-tooltip></el-table-column>
				<el-table-column label="单位" prop="basicUnit" show-overflow-tooltip></el-table-column>
				<el-table-column label="有效期至" prop="endEffectiveTime" show-overflow-tooltip></el-table-column>
				<el-table-column label="操作" width="200" fixed="right">
					<template #default="scope">
						<!-- 修改信息 -->
						<el-button  text type="primary" @click="LogDetailRef.openDialog(scope.row.id)"> 明细 </el-button>
					</template>
				</el-table-column>
			</el-table>
		</div>
		<log-detail ref="LogDetailRef"></log-detail>
	</div>
</template>

<script lang="ts" setup>
import {  getObj,downPdf } from '/@/api/storage/warehouseManagement/purchase';
const LogDetail = defineAsyncComponent(() => import('./detail.vue'));

import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
const route = useRoute();

// 动态引入组件
const LogDetailRef = ref();

const { t } = useI18n();

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({});
const {  downBlobFile, tableStyle } = useTable(state);

const router = useRouter();
const returnClick = () => {
	router.replace({ path: '/storage/warehouseManagement/purchase/index' });
};


const detailsData = ref<any>();
onMounted(async () => {
	await getObj(route.query?.id).then((res) => {
		detailsData.value = res.data;
	});
});
const exportPdf = async () => {


	downBlobFile(`/admin/entryWarehouseBill/pdfEntryWarehouseBillById/${route.query?.id}`,'', '入库单.pdf');


}
</script>

<style lang="scss" scoped>
pre code.hljs {
	width: 65%;
}
</style>

<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="mb8 w-full">
					<div class="float-left">
						<el-form ref="queryRef" :inline="true" :model="state.queryForm" @keyup.enter="getDataList">
							<el-form-item label="入库单编号">
								<el-input v-model="state.queryForm.billCode" placeholder="请输入入库单编号" clearable style="max-width: 180px" />
							</el-form-item>
							<el-form-item label="入库单状态">
								<el-select placeholder="请选择入库单状态" clearable v-model="state.queryForm.billStatus" class="!max-w-[180px]">
									<el-option
										:key="item.value"
										:label="item.label"
										:value="item.value"
										v-for="item in [
											{ label: '待入库', value: 0 },
											{ label: '已入库', value: 1 },
											{ label: '已上架', value: 2 },
										]"
									/>
								</el-select>
							</el-form-item>
							<el-form-item label="入库仓库">
								<el-select placeholder="请选择入库仓库" clearable v-model="state.queryForm.warehouseId" class="!max-w-[180px]">
									<el-option :key="item.value" :label="item.warehouseName" :value="item.id" v-for="item in warehouseData" />
								</el-select>
							</el-form-item>
							<el-form-item label="入库时间" >
								<el-date-picker
									:end-placeholder="$t('syslog.inputEndPlaceholderTip')"
									:start-placeholder="$t('syslog.inputStartPlaceholderTip')"
									range-separator="-"
									type="daterange"
									@change="dateChange"
									v-model="state.queryForm.time"
									value-format="YYYY-MM-DD HH:mm:ss"
									format="YYYY-MM-DD"
								/>
							</el-form-item>

							<el-form-item>
								<el-button icon="Search" type="primary" @click="getDataList">{{ $t('common.queryBtn') }}</el-button>
							</el-form-item>
						</el-form>
					</div>
					<div class="float-right">
						<el-button icon="folder-add" type="primary" @click="routerClick()">
							{{ $t('common.addBtn') }}
						</el-button>
					</div>
				</div>
			</el-row>

			<el-table
				v-loading="state.loading"
				:data="state.dataList"
				row-key="userId"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column label="序号" type="index" width="60" fixed="left" />
				<el-table-column label="入库单编号" prop="billCode" show-overflow-tooltip></el-table-column>

				<el-table-column label="入库单状态" show-overflow-tooltip>
					<template #default="scope">
						{{ inventoryStatus[scope.row.billStatus] }}
					</template>
				</el-table-column>
				<el-table-column label="入库仓库" prop="warehouseName" show-overflow-tooltip></el-table-column>
				<el-table-column label="入库部门" prop="entryDept" show-overflow-tooltip></el-table-column>
				<el-table-column label="入库人员" prop="entryUser" show-overflow-tooltip></el-table-column>
				<el-table-column label="入库时间" prop="entryTime" show-overflow-tooltip></el-table-column>
				<el-table-column label="创建人" prop="createUser" show-overflow-tooltip></el-table-column>
				<el-table-column label="创建时间" prop="createTime" show-overflow-tooltip></el-table-column>
				<el-table-column :label="$t('common.action')" width="200" fixed="right">
					<template #default="scope">
						<!-- 核验入库 -->
						<el-button text type="primary" @click="verificationClick(scope.row.id)" v-if="scope.row.billStatus == '0'"> 核验入库 </el-button>
						<!-- 修改信息 -->
						<el-button icon="edit-pen" text type="primary" @click="routerClick(scope.row.id)" v-if="scope.row.billStatus == '0'">
							{{ $t('common.editBtn') }}
						</el-button>
						<!-- 删除 -->
						<el-button icon="delete" @click="handleDelete(scope.row.id)" text type="primary" v-if="scope.row.billStatus == '0'"
							>{{ $t('common.delBtn') }}
						</el-button>
						<!-- 查看 入库单 -->
						<el-button text type="primary" @click="formClick(scope.row.id)" v-if="scope.row.billStatus != '0'"> 查看 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination v-bind="state.pagination" @current-change="currentChangeHandle" @size-change="sizeChangeHandle"> </pagination>
		</div>
	</div>
</template>

<script lang="ts" name="systemUser" setup>
import { delObj, pageList, getWarehouse } from '/@/api/storage/warehouseManagement/purchase';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
import { useDict } from '/@/hooks/dict';
import { getConfigurationObj } from '/@/api/storage/rfidSettings';

// @ts-ignore
import { getLodop } from '../../../../../public/plugin/Lodop/LodopFuncs'; //导入模块

// 定义查询字典
const { warnType } = useDict('warnType');
// 动态引入组件

const { t } = useI18n();

// 定义变量内容
const queryRef = ref();

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		billCode: '',
		billType: '',
		warehouseId: '',
		time: '',
		beginEntryTime: '',
		endEntryTime: '',
	},
	pageList: pageList,
});
const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile, tableStyle } = useTable(state);

const inventoryStatus: any = {
	'0': '待入库',
	'1': '已入库',
	'2': '已上架',
};
const dateChange = (value: any) => {
	state.queryForm.beginEntryTime = '';
	state.queryForm.endEntryTime = '';
	if (!Array.isArray(value)) return;
	state.queryForm.beginEntryTime = value[0];
	state.queryForm.endEntryTime = value[1].replace(/\d{2}:\d{2}:\d{2}$/, '23:59:59');
};
// 删除操作
const handleDelete = async (id: string) => {
	try {
		await useMessageBox().confirm(t('common.delConfirmText'));
	} catch {
		return;
	}

	try {
		await delObj(id);
		getDataList();
		useMessage().success(t('common.delSuccessText'));
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};

//查看入库单页面
const formClick = (id?: any) => {
	router.push({
		path: '/storage/warehouseManagement/purchase/form',
		query: { id: id, notCreateTags: 'true' },
	});
};
//核验入库
const verificationClick = (id?: any) => {
	router.push({
		path: '/storage/warehouseManagement/purchase/verification',
		query: { id: id, notCreateTags: 'true' },
	});
};

// 获取仓库数据
const warehouseData = ref<any[]>([]);
const getWarehouseData = () => {
	getWarehouse().then((res: any) => {
		warehouseData.value = res.data;
	});
};
//新增 修改页面
const router = useRouter();
const routerClick = (id?: any) => {
	const tagsViewName = id ? '采购入库修改' : '采购入库新增';
	router.push({
		path: '/storage/warehouseManagement/purchase/add',
		query: { id: id, tagsViewName: tagsViewName, notCreateTags: 'true' },
	});
};

onMounted(() => {
	getWarehouseData();
});
</script>

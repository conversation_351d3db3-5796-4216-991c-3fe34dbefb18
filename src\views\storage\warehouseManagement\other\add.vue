<template>
	<div class="layout-padding">
		<splitpanes>
			<pane size="15">
				<div class="layout-padding-auto layout-padding-view">
					<el-input v-model="state.queryForm.phone" placeholder="请通过条码扫码录入" clearable style="width: 95%" />
					<el-row class="mt3 mb5">
						<div style="width: 100%; display: flex; justify-content: space-between; align-items: center">
							<div>
								<span>读取标签数：</span><span class="text-blue-500">{{ barList.length }}</span>
							</div>
							<div>
								<el-button
									text
									type="primary"
									icon="delete"
									@click="
										() => {
											barList = [];
										}
									"
								>
								</el-button>
							</div>
						</div>
					</el-row>
					<ul style="overflow: auto; height: 80vh">
						<li v-for="i in barList" :key="i" class="infinite-list-item">{{ i }}</li>
					</ul>
					<el-row class="mt12">
						<div style="width: 100%; display: flex; justify-content: space-evenly; align-items: center">
							<el-button type="primary">读标签 </el-button>
							<el-button color="#FF7D00" type="primary" style="color: white !important" @click="matchClick">匹配 </el-button>
						</div>
					</el-row>
				</div>
			</pane>
			<!-- 右侧 -->
			<pane class="ml8">
				<div class="layout-padding-auto layout-padding-view">
					<el-row class="mt7">
						<div class="text-xl font-bold">新增入库</div>
					</el-row>
					<el-row class="mt20 mb10">
						<el-form ref="queryRef" :inline="false" label-width="100px">
							<el-form-item label="入库仓库">
								<el-select placeholder="请选择入库仓库" v-model="warehouseId" class="!w-[200px]">
									<el-option :key="item.value" :label="item.warehouseName" :value="item.id" v-for="item in warehouseData" />
								</el-select>
							</el-form-item>
						</el-form>
					</el-row>
					<el-table
						height="calc(100vh - 270px)"
						:data="tableData"
						border
						empty-text="暂无数据，请从左侧添加入库信息"
						:cell-style="tableStyle.cellStyle"
						:header-cell-style="tableStyle.headerCellStyle"
						ref="tableRefs"
						style="width: 100%"
					>
						<el-table-column label="序号" type="index" width="60" fixed="left" />
						<el-table-column label="物资编码" prop="materialCode" fixed="left" show-overflow-tooltip></el-table-column>
						<el-table-column label="物资标识" prop="materialIdentify" show-overflow-tooltip></el-table-column>
						<el-table-column label="物资名称" prop="materialName" show-overflow-tooltip width="200"></el-table-column>
						<el-table-column label="单位" prop="basicUnit" show-overflow-tooltip width="60"></el-table-column>
						<el-table-column label="条码数量" show-overflow-tooltip width="260">
							<template #default="scope">
								{{ scope.row.barCode ? scope.row.barCode.split(',')?.length : '' }}
							</template>
						</el-table-column>
						<el-table-column label="条码明细" show-overflow-tooltip width="250" class-name="custom-class">
							<template #header>
								<div>
									<span>条码明细</span>
									<el-tooltip class="box-item" effect="light" content="全部收起" placement="top-start">
										<el-button
											v-if="tableData.some((row:any) => needsExpansion(row.barCode))"
											link
											type="primary"
											size="small"
											@click="toggleAllExpand"
										>
											<img :src="zdImg" v-show="isAllExpanded" />
										</el-button>
									</el-tooltip>
								</div>
							</template>
							<template #default="scope">
								<div v-if="scope.row.barCode">
									<div class="flex justify-items-start">
										<div>
											<div v-for="(code, index) in getDisplayCodes(scope.row)" :key="index">
												{{ code }}
											</div>
										</div>

										<el-button
											v-if="needsExpansion(scope.row.barCode)"
											link
											type="primary"
											size="small"
											@click="toggleExpand(scope.row, scope.$index)"
										>
											<img :src="scope.row.isExpanded ? zdImg : zkImg" :alt="scope.row.isExpanded ? '收起' : '展开'" />
										</el-button>
									</div>
								</div>
							</template>
						</el-table-column>
					</el-table>
					<el-row>
						<div class="w-full mt15">
							<div class="float-left">
								物资清单（当前条码数量 {{ tableData.reduce((sum: any, item: any) => sum + item.barCode.split(',')?.length, 0) }} ）
							</div>
							<div class="float-right">
								<el-button @click="returnClick">{{ $t('common.cancelButtonText') }}</el-button>
								<el-button @click="confirmClick" type="primary" :disabled="loading">{{ $t('common.confirmButtonText') }}</el-button>
							</div>
						</div>
					</el-row>
				</div>
			</pane>
		</splitpanes>
		<error-bar ref="errorDialogRef" />
	</div>
</template>

<script lang="ts" name="systemUser" setup>
import { getWarehouse, filterTags, materialInfo, addObj } from '/@/api/storage/warehouseManagement/other';
const ErrorBar = defineAsyncComponent(() => import('./errorBar.vue'));

import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
// 动态引入组件

const { t } = useI18n();

// 定义变量内容
const queryRef = ref();
const errorDialogRef = ref<any>();
// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({});

const { tableStyle } = useTable(state);

//确认btn
const loading = ref(false);

//入库仓库
let warehouseId = ref('');

//右侧提交
const confirmClick = async () => {
	try {
		loading.value = true;
		const billDetailList = tableData.value.map((item: any) => {
			return {
				materialCode: item.materialCode,
				materialCatalogId: item.id,
				materialBarList: item.barCode.split(','),
			};
		});
		await addObj({
			warehouseId: warehouseId.value,
			billDetailList,
		});
		useMessage().success(t('common.addSuccessText'));
	} catch (err: any) {
		useMessage().error(err.msg);
	} finally {
		loading.value = false;
	}
};

const tableData = ref<any>([]);
//匹配事件
const matchClick = async () => {
	if (!barList.value.length) return useMessage().error('请先读取标签');
	if (warehouseId.value == '') return useMessage().error('请选择入库仓库');
	if (judgingQuantity()) return;
	//TODO 关闭读标签功能
	await goFilterTags();
	const obj = correctBarList.value.reduce(
		(acc: any, item: any) => ((acc[item.slice(12, 18)] = acc[item.slice(12, 18)] ? [...acc[item.slice(12, 18)], item] : [item]), acc),
		{}
	);
	const newCodeList = tableData.value.length
		? Object.keys(obj).filter((code) => !new Set(tableData.value.map((item: any) => item.materialCode)).has(code))
		: Object.keys(obj);
	if (tableData.value.length + newCodeList.length > 50) {
		useMessage().error('一次入库物资数量不能超过50');
		return;
	}
	if (newCodeList.length) {
		await getMaterialInfo(newCodeList);
	}
	tableData.value.forEach((item: any) => {
		const newBarCode = obj[item.materialCode];
		if (newBarCode) {
			const currentBarCodes = item.barCode ? item.barCode.split(',') : [];
			item.barCode = [...new Set([...currentBarCodes, ...newBarCode])].join(',');
		}
	});
	//TODO 匹配成功后，开启读取标签功能
};
const goFilterTags = async () => {
	correctBarList.value = [];
	try {
		const { data } = await filterTags({
			warehouseId: warehouseId.value,
			materialBarList: barList.value,
		});
		if (data && data.length) {
			errorDialogRef.value.openDialog(data);
			correctBarList.value = barList.value.filter((item: any) => !data.includes(item));
		} else {
			correctBarList.value = [...barList.value];
		}
	} catch (error) {}
};
const getMaterialInfo = async (codeList: string[]) => {
	const promises = codeList.map((code) => materialInfo(code));
	const results = await Promise.all(promises);
	results.forEach((item) => {
		if (item.data) {
			tableData.value.push({
				...item.data,
				isExpanded: false,
			});
		}
	});
};
const judgingQuantity = () => {
	const totalQuantity = tableData.value.reduce((sum: any, item: any) => sum + item.barCode.split(',').length, 0);
	if (totalQuantity > 2000) {
		useMessage().error('一次入库条码数量不能超过2000');
		return true;
	}
	if (tableData.value.length > 50) {
		useMessage().error('一次入库物资数量不能超过50');
		return true;
	}
};
const correctBarList = ref<any>([]);
const barList = ref<any>([]);

// 获取仓库数据
const warehouseData = ref<any[]>([]);
const getWarehouseData = () => {
	getWarehouse().then((res: any) => {
		warehouseData.value = res.data;
	});
};
//返回上级菜单
const router = useRouter();
const returnClick = () => {
	router.replace({
		path: '/storage/warehouseManagement/other/index',
	});
};
onMounted(() => {
	getWarehouseData();
});

// 表格内展开折叠
import zkImg from '/@/assets/flod.png';
import zdImg from '/@/assets/open.png';
const MAX_DISPLAY_LINES = 2;
//table ref
const tableRefs = ref();
//表格数据
//全部数据，截取最大行数   截取字段为row.barCode
const getDisplayCodes = (row: any) => {
	const barCode = row.barCode;
	if (!barCode) return [];
	const codes = barCode.split(',');
	if (codes.length > MAX_DISPLAY_LINES && !row.isExpanded) {
		return [...codes.slice(0, MAX_DISPLAY_LINES - 1)];
	}
	return codes;
};
//table滚动到指定行
const scrollToRow = (tableRef: any, rowIndex: number) => {
	nextTick(() => {
		if (tableRef.value) {
			// @ts-ignore
			const tableBody = tableRef.value.$el.querySelector('.el-table__body-wrapper');
			if (tableBody) {
				const row = tableBody.querySelectorAll('.el-table__row')[rowIndex - 1];
				if (row) {
					row.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
				}
			}
		}
	});
};
//将所有行 收起
const toggleAllExpand = () => {
	tableData.value.forEach((row: any) => {
		row.isExpanded = false;
	});
};
//有展开的行  则展示表头收起图标
const isAllExpanded = computed(() => tableData.value.some((row: any) => row.isExpanded));
//判断长度需要展开字段的数据长度
const needsExpansion = (barCode?: string) => {
	return barCode ? barCode.split(',').length > MAX_DISPLAY_LINES : false;
};
//行内展开收起事件
const toggleExpand = (row: any, rowIndex: any) => {
	row.isExpanded = !row.isExpanded;
	if (!row.isExpanded) scrollToRow(tableRefs, rowIndex + 1);
};
</script>
<style lang="scss"></style>

import request from '/@/utils/request';
//分页查询
export const pageList = (params?: Object) => {
	return request({
		url: '/admin/materialMatingGroup/getMaterialMatingGroupPage',
		method: 'get',
		params,
	});
};
//新增
export const addObj = (obj: Object) => {
	return request({
		url: '/admin/materialMatingGroup/addMaterialMatingGroup',
		method: 'post',
		data: obj,
	});
};
//修改
export const putObj = (obj: Object) => {
	return request({
		url: '/admin/materialMatingGroup/updateMaterialMatingGroup',
		method: 'post',
		data: obj,
	});
};
//删除
export function delObj(id?: string) {
	return request({
		url: '/admin/materialMatingGroup/deleteMaterialMatingGroupById/' + id,
		method: 'get',
	});
}
//详情
export const getObj = (id: String) => {
	return request({
		url: '/admin/materialMatingGroup/getMaterialMatingGroupById/' + id,
		method: 'get',
	});
};
//启用禁用
export const switchRow = (obj: Object) => {
	return request({
		url: '/admin/materialMatingGroup/updateMaterialMatingGroupStatus',
		method: 'post',
		data: obj,
	});
};

<!-- 通知记录 -->
<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row>
				<div class="w-full">
					<div class="float-left">
						<el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList" ref="queryRef">
							<el-form-item label="预警类型" prop="logType">
								<el-select placeholder="请选择预警类型" clearable v-model="state.queryForm.logType">
									<el-option :key="item.value" :label="item.label" :value="item.value" v-for="item in warnType" />
								</el-select>
							</el-form-item>
							<el-form-item label="时间" prop="createTime">
								<el-date-picker
									:end-placeholder="$t('syslog.inputEndPlaceholderTip')"
									:start-placeholder="$t('syslog.inputStartPlaceholderTip')"
									range-separator="-"
									type="datetimerange"
									v-model="state.queryForm.createTime"
									value-format="YYYY-MM-DD HH:mm:ss"
								/>
							</el-form-item>
							<el-form-item>
								<el-button @click="getDataList" icon="Search" type="primary">{{ $t('common.queryBtn') }}</el-button>
							</el-form-item>
						</el-form>
					</div>
					<div class="float-right">
						<el-button @click="getDataList" type="primary">导出</el-button>
					</div>
				</div>
			</el-row>

			<el-table
				ref="tableRef"
				:data="state.dataList"
				@selection-change="handleSelectionChange"
				@sort-change="sortChangeHandle"
				v-loading="state.loading"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
			>
				<el-table-column label="序号" type="index" width="60" />

				<el-table-column label="预警时间" prop="title" show-overflow-tooltip></el-table-column>
				<el-table-column label="预警类型" prop="title" show-overflow-tooltip></el-table-column>
				<el-table-column label="预警通知人" prop="title" show-overflow-tooltip></el-table-column>
				<el-table-column label="预警内容" prop="title" show-overflow-tooltip width="600"></el-table-column>
				<el-table-column label="预警方式" prop="title" show-overflow-tooltip></el-table-column>
			</el-table>

			<pagination @current-change="currentChangeHandle" @size-change="sizeChangeHandle" v-bind="state.pagination"></pagination>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { BasicTableProps, useTable } from '/@/hooks/table';
import { delObj, pageList } from '/@/api/admin/log';
import { useI18n } from 'vue-i18n';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useDict } from '/@/hooks/dict';

const LogDetailRef = ref();

// 定义查询字典
const { warnType } = useDict('warnType');
const { t } = useI18n();

// 定义变量内容
const queryRef = ref();
const showSearch = ref(true);

// 多选rows
const selectObjs = ref([]) as any;
// 是否可以多选
const multiple = ref(true);
let tableRef = ref(null);

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		logType: '',
		createTime: '',
		serviceId: '',
	},
	selectObjs: [],
	pageList: pageList,
	descs: ['create_time'],
	createdIsNeed: false,
});

//  table hook
const { downBlobFile, getDataList, currentChangeHandle: baseCurrentChangeHandle, sortChangeHandle, sizeChangeHandle, tableStyle } = useTable(state);

// 分页事件
const currentChangeHandle = (page: number) => {
	// Reset table scroll position to top
	tableRef.value?.setScrollTop(0);
	// Call the original handler
	baseCurrentChangeHandle(page);
};

// 清空搜索条件
const resetQuery = () => {
	queryRef.value?.resetFields();
	getDataList();
};

// 导出excel
const exportExcel = () => {
	downBlobFile('/admin/log/export', state.queryForm, 'log.xlsx');
};

// 多选事件
const handleSelectionChange = (objs: { id: string }[]) => {
	selectObjs.value = objs.map(({ id }) => id);
	multiple.value = !objs.length;
};

// 删除操作
const handleDelete = async (ids: string[]) => {
	try {
		await useMessageBox().confirm(t('common.delConfirmText'));
	} catch {
		return;
	}

	try {
		await delObj(ids);
		getDataList();
		useMessage().success(t('common.delSuccessText'));
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};

// onMounted 通过路由参数给  serviceId 赋值
const route = useRoute();
onMounted(() => {
	const { serviceId } = route.query;
	if (serviceId) {
		state.queryForm.serviceId = serviceId;
	}
	getDataList();
});
</script>

<style lang="scss" scoped>
pre code.hljs {
	width: 65%;
}
</style>
